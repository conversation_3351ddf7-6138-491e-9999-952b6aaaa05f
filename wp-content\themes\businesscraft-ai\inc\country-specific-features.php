<?php
/**
 * Country-Specific Features Enhancement System for ChatGABI
 * 
 * This file provides country-specific business features including regulatory
 * compliance, local market intelligence, and region-specific business tools.
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class ChatGABI_Country_Specific_Features {
    
    private $country_features;
    private $regulatory_frameworks;
    private $business_registration_processes;
    private $tax_systems;
    private $market_intelligence;
    
    public function __construct() {
        $this->init_country_features();
        $this->init_regulatory_frameworks();
        $this->init_business_registration_processes();
        $this->init_tax_systems();
        $this->init_market_intelligence();
    }
    
    /**
     * Initialize country-specific features
     */
    private function init_country_features() {
        $this->country_features = array(
            'GH' => array(
                'business_environment' => array(
                    'ease_of_doing_business_rank' => 118,
                    'startup_time_days' => 14,
                    'minimum_capital_required' => 'GHS 1 (symbolic)',
                    'key_sectors' => array('Agriculture', 'Mining', 'Oil & Gas', 'ICT', 'Tourism'),
                    'growth_sectors' => array('Fintech', 'Agritech', 'E-commerce', 'Renewable Energy'),
                    'government_initiatives' => array('One District One Factory', 'Ghana Beyond Aid', 'Digital Ghana Agenda')
                ),
                'financial_ecosystem' => array(
                    'mobile_money_penetration' => '78%',
                    'banking_penetration' => '58%',
                    'microfinance_institutions' => 'Strong presence',
                    'venture_capital_availability' => 'Growing',
                    'government_funding_schemes' => array('MASLOC', 'NBSSI', 'Ghana EXIM Bank')
                ),
                'digital_infrastructure' => array(
                    'internet_penetration' => '68%',
                    'mobile_penetration' => '138%',
                    'fiber_optic_coverage' => 'Major cities',
                    'digital_payment_adoption' => 'High',
                    'e_government_services' => 'Developing'
                ),
                'cultural_business_factors' => array(
                    'business_language' => 'English (official), Twi, Ga, Ewe',
                    'business_hours' => '8:00 AM - 5:00 PM (Mon-Fri)',
                    'important_holidays' => array('Independence Day (March 6)', 'Farmers Day (December)', 'Homowo Festival'),
                    'networking_culture' => 'Relationship-based, community-oriented',
                    'decision_making_style' => 'Consensus-seeking, elder consultation'
                )
            ),
            'KE' => array(
                'business_environment' => array(
                    'ease_of_doing_business_rank' => 56,
                    'startup_time_days' => 11,
                    'minimum_capital_required' => 'KES 100,000 (for limited companies)',
                    'key_sectors' => array('Agriculture', 'Tourism', 'Manufacturing', 'ICT', 'Financial Services'),
                    'growth_sectors' => array('Fintech', 'Healthtech', 'Edtech', 'Cleantech', 'Logistics'),
                    'government_initiatives' => array('Vision 2030', 'Big Four Agenda', 'Digital Economy Blueprint')
                ),
                'financial_ecosystem' => array(
                    'mobile_money_penetration' => '96%',
                    'banking_penetration' => '89%',
                    'microfinance_institutions' => 'Well established',
                    'venture_capital_availability' => 'Strong',
                    'government_funding_schemes' => array('Youth Enterprise Development Fund', 'Women Enterprise Fund', 'Uwezo Fund')
                ),
                'digital_infrastructure' => array(
                    'internet_penetration' => '87%',
                    'mobile_penetration' => '109%',
                    'fiber_optic_coverage' => 'Extensive',
                    'digital_payment_adoption' => 'Very High (M-Pesa leader)',
                    'e_government_services' => 'Advanced'
                ),
                'cultural_business_factors' => array(
                    'business_language' => 'English (official), Swahili (national)',
                    'business_hours' => '8:00 AM - 5:00 PM (Mon-Fri)',
                    'important_holidays' => array('Jamhuri Day (December 12)', 'Mashujaa Day (October 20)', 'Labour Day (May 1)'),
                    'networking_culture' => 'Professional, innovation-focused',
                    'decision_making_style' => 'Data-driven, collaborative'
                )
            ),
            'NG' => array(
                'business_environment' => array(
                    'ease_of_doing_business_rank' => 131,
                    'startup_time_days' => 19,
                    'minimum_capital_required' => 'NGN 100,000 (for private limited companies)',
                    'key_sectors' => array('Oil & Gas', 'Agriculture', 'Telecommunications', 'Banking', 'Manufacturing'),
                    'growth_sectors' => array('Fintech', 'E-commerce', 'Entertainment', 'Logistics', 'Renewable Energy'),
                    'government_initiatives' => array('Economic Recovery and Growth Plan', 'National Digital Economy Policy', 'Nigeria Startup Act')
                ),
                'financial_ecosystem' => array(
                    'mobile_money_penetration' => '45%',
                    'banking_penetration' => '45%',
                    'microfinance_institutions' => 'Extensive network',
                    'venture_capital_availability' => 'Rapidly growing',
                    'government_funding_schemes' => array('Bank of Industry', 'NIRSAL', 'Tony Elumelu Foundation')
                ),
                'digital_infrastructure' => array(
                    'internet_penetration' => '51%',
                    'mobile_penetration' => '104%',
                    'fiber_optic_coverage' => 'Major cities and corridors',
                    'digital_payment_adoption' => 'Growing rapidly',
                    'e_government_services' => 'Developing'
                ),
                'cultural_business_factors' => array(
                    'business_language' => 'English (official), Hausa, Yoruba, Igbo',
                    'business_hours' => '8:00 AM - 5:00 PM (Mon-Fri)',
                    'important_holidays' => array('Independence Day (October 1)', 'Democracy Day (June 12)', 'Workers Day (May 1)'),
                    'networking_culture' => 'Relationship-based, status-conscious',
                    'decision_making_style' => 'Hierarchical, ambitious goal-setting'
                )
            ),
            'ZA' => array(
                'business_environment' => array(
                    'ease_of_doing_business_rank' => 84,
                    'startup_time_days' => 40,
                    'minimum_capital_required' => 'ZAR 1 (symbolic)',
                    'key_sectors' => array('Mining', 'Manufacturing', 'Financial Services', 'Agriculture', 'Tourism'),
                    'growth_sectors' => array('Renewable Energy', 'Fintech', 'E-commerce', 'Agritech', 'Healthtech'),
                    'government_initiatives' => array('National Development Plan 2030', 'Economic Reconstruction and Recovery Plan', 'Digital Economy Masterplan')
                ),
                'financial_ecosystem' => array(
                    'mobile_money_penetration' => '25%',
                    'banking_penetration' => '93%',
                    'microfinance_institutions' => 'Moderate presence',
                    'venture_capital_availability' => 'Established',
                    'government_funding_schemes' => array('SEDA', 'IDC', 'NEF', 'NYDA')
                ),
                'digital_infrastructure' => array(
                    'internet_penetration' => '68%',
                    'mobile_penetration' => '91%',
                    'fiber_optic_coverage' => 'Good in urban areas',
                    'digital_payment_adoption' => 'Moderate',
                    'e_government_services' => 'Well developed'
                ),
                'cultural_business_factors' => array(
                    'business_language' => 'English (business), Afrikaans, Zulu, Xhosa (11 official languages)',
                    'business_hours' => '8:00 AM - 5:00 PM (Mon-Fri)',
                    'important_holidays' => array('Freedom Day (April 27)', 'Heritage Day (September 24)', 'Human Rights Day (March 21)'),
                    'networking_culture' => 'Professional, transformation-focused',
                    'decision_making_style' => 'Consensus-building, quality-focused'
                )
            )
        );
    }
    
    /**
     * Initialize regulatory frameworks
     */
    private function init_regulatory_frameworks() {
        $this->regulatory_frameworks = array(
            'GH' => array(
                'business_registration' => array(
                    'authority' => 'Registrar General\'s Department',
                    'online_portal' => 'https://rg.gov.gh',
                    'required_documents' => array('Application Form', 'Memorandum and Articles', 'Statutory Declaration'),
                    'fees' => 'GHS 250 - GHS 500',
                    'processing_time' => '3-7 days'
                ),
                'tax_registration' => array(
                    'authority' => 'Ghana Revenue Authority (GRA)',
                    'tax_types' => array('Income Tax', 'VAT', 'NHIL', 'GETFund Levy'),
                    'vat_threshold' => 'GHS 200,000 annually',
                    'corporate_tax_rate' => '25%'
                ),
                'sector_regulations' => array(
                    'financial_services' => 'Bank of Ghana, Securities and Exchange Commission',
                    'telecommunications' => 'National Communications Authority',
                    'mining' => 'Minerals Commission',
                    'oil_and_gas' => 'Petroleum Commission'
                )
            ),
            'KE' => array(
                'business_registration' => array(
                    'authority' => 'Business Registration Service (BRS)',
                    'online_portal' => 'https://brs.go.ke',
                    'required_documents' => array('CR12 Form', 'Memorandum and Articles', 'PIN Certificate'),
                    'fees' => 'KES 10,000 - KES 15,000',
                    'processing_time' => '1-3 days'
                ),
                'tax_registration' => array(
                    'authority' => 'Kenya Revenue Authority (KRA)',
                    'tax_types' => array('Income Tax', 'VAT', 'PAYE', 'Withholding Tax'),
                    'vat_threshold' => 'KES 5,000,000 annually',
                    'corporate_tax_rate' => '30%'
                ),
                'sector_regulations' => array(
                    'financial_services' => 'Central Bank of Kenya, Capital Markets Authority',
                    'telecommunications' => 'Communications Authority of Kenya',
                    'energy' => 'Energy and Petroleum Regulatory Authority',
                    'agriculture' => 'Agriculture and Food Authority'
                )
            ),
            'NG' => array(
                'business_registration' => array(
                    'authority' => 'Corporate Affairs Commission (CAC)',
                    'online_portal' => 'https://pre.cac.gov.ng',
                    'required_documents' => array('CAC Forms', 'Memorandum and Articles', 'Compliance Certificate'),
                    'fees' => 'NGN 10,000 - NGN 50,000',
                    'processing_time' => '5-10 days'
                ),
                'tax_registration' => array(
                    'authority' => 'Federal Inland Revenue Service (FIRS)',
                    'tax_types' => array('Company Income Tax', 'VAT', 'WHT', 'Tertiary Education Tax'),
                    'vat_threshold' => 'NGN 25,000,000 annually',
                    'corporate_tax_rate' => '30%'
                ),
                'sector_regulations' => array(
                    'financial_services' => 'Central Bank of Nigeria, Securities and Exchange Commission',
                    'telecommunications' => 'Nigerian Communications Commission',
                    'oil_and_gas' => 'Department of Petroleum Resources',
                    'power' => 'Nigerian Electricity Regulatory Commission'
                )
            ),
            'ZA' => array(
                'business_registration' => array(
                    'authority' => 'Companies and Intellectual Property Commission (CIPC)',
                    'online_portal' => 'https://www.cipc.co.za',
                    'required_documents' => array('CoR 14.1 Form', 'Memorandum of Incorporation', 'Notice of Incorporation'),
                    'fees' => 'ZAR 175 - ZAR 500',
                    'processing_time' => '5-10 days'
                ),
                'tax_registration' => array(
                    'authority' => 'South African Revenue Service (SARS)',
                    'tax_types' => array('Income Tax', 'VAT', 'PAYE', 'Skills Development Levy'),
                    'vat_threshold' => 'ZAR 1,000,000 annually',
                    'corporate_tax_rate' => '28%'
                ),
                'sector_regulations' => array(
                    'financial_services' => 'South African Reserve Bank, Financial Sector Conduct Authority',
                    'telecommunications' => 'Independent Communications Authority of South Africa',
                    'mining' => 'Department of Mineral Resources and Energy',
                    'energy' => 'National Energy Regulator of South Africa'
                )
            )
        );
    }
    
    /**
     * Initialize business registration processes
     */
    private function init_business_registration_processes() {
        $this->business_registration_processes = array(
            'GH' => array(
                'steps' => array(
                    '1' => 'Name reservation at Registrar General\'s Department',
                    '2' => 'Prepare incorporation documents',
                    '3' => 'Submit application with required fees',
                    '4' => 'Obtain Certificate of Incorporation',
                    '5' => 'Register for taxes with GRA',
                    '6' => 'Register with Social Security and National Insurance Trust (SSNIT)',
                    '7' => 'Obtain business operating permit from local assembly'
                ),
                'estimated_cost' => 'GHS 1,000 - GHS 2,000',
                'estimated_time' => '2-3 weeks',
                'common_challenges' => array(
                    'Name availability delays',
                    'Document preparation complexity',
                    'Multiple agency visits required'
                )
            ),
            'KE' => array(
                'steps' => array(
                    '1' => 'Name search and reservation online',
                    '2' => 'Prepare incorporation documents',
                    '3' => 'Submit application through eCitizen portal',
                    '4' => 'Obtain Certificate of Incorporation',
                    '5' => 'Register for KRA PIN',
                    '6' => 'Register with NSSF and NHIF',
                    '7' => 'Obtain county business permit'
                ),
                'estimated_cost' => 'KES 15,000 - KES 25,000',
                'estimated_time' => '1-2 weeks',
                'common_challenges' => array(
                    'Digital platform navigation',
                    'County permit variations',
                    'Compliance requirements complexity'
                )
            ),
            'NG' => array(
                'steps' => array(
                    '1' => 'Name availability search at CAC',
                    '2' => 'Reserve company name',
                    '3' => 'Prepare incorporation documents',
                    '4' => 'Submit application to CAC',
                    '5' => 'Obtain Certificate of Incorporation',
                    '6' => 'Register for Tax Identification Number (TIN)',
                    '7' => 'Register with Pension Fund Administrator',
                    '8' => 'Obtain state and local government permits'
                ),
                'estimated_cost' => 'NGN 50,000 - NGN 100,000',
                'estimated_time' => '3-4 weeks',
                'common_challenges' => array(
                    'Multiple state requirements',
                    'Documentation complexity',
                    'Processing delays'
                )
            ),
            'ZA' => array(
                'steps' => array(
                    '1' => 'Name availability check at CIPC',
                    '2' => 'Reserve company name',
                    '3' => 'Prepare Memorandum of Incorporation',
                    '4' => 'Submit incorporation application',
                    '5' => 'Obtain Certificate of Incorporation',
                    '6' => 'Register for tax with SARS',
                    '7' => 'Register with UIF and Compensation Fund',
                    '8' => 'Obtain municipal trading license'
                ),
                'estimated_cost' => 'ZAR 2,000 - ZAR 5,000',
                'estimated_time' => '2-4 weeks',
                'common_challenges' => array(
                    'B-BBEE compliance requirements',
                    'Municipal license variations',
                    'Skills development levy registration'
                )
            )
        );
    }
    
    /**
     * Initialize tax systems
     */
    private function init_tax_systems() {
        $this->tax_systems = array(
            'GH' => array(
                'corporate_income_tax' => '25%',
                'vat_rate' => '12.5% + 2.5% NHIL + 2.5% GETFund',
                'withholding_tax' => '5-20% depending on transaction type',
                'small_business_incentives' => array(
                    'First 5 years - Reduced rates for new businesses',
                    'SME tax rate - 25% (standard rate)',
                    'Turnover tax - Available for businesses with turnover below GHS 500,000'
                ),
                'filing_deadlines' => array(
                    'monthly_vat' => '15th of following month',
                    'annual_income_tax' => '4 months after year end',
                    'paye' => '15th of following month'
                )
            ),
            'KE' => array(
                'corporate_income_tax' => '30%',
                'vat_rate' => '16%',
                'withholding_tax' => '5-20% depending on transaction type',
                'small_business_incentives' => array(
                    'Turnover tax - 1% for businesses with turnover KES 1M-50M',
                    'Presumptive tax - For small informal businesses',
                    'Manufacturing under bond - Various incentives'
                ),
                'filing_deadlines' => array(
                    'monthly_vat' => '20th of following month',
                    'annual_income_tax' => '6 months after year end',
                    'paye' => '9th of following month'
                )
            ),
            'NG' => array(
                'corporate_income_tax' => '30%',
                'vat_rate' => '7.5%',
                'withholding_tax' => '5-10% depending on transaction type',
                'small_business_incentives' => array(
                    'Small company rate - 20% for companies with turnover below NGN 25M',
                    'Pioneer status - Tax holidays for qualifying industries',
                    'Investment allowances - Various capital allowances'
                ),
                'filing_deadlines' => array(
                    'monthly_vat' => '21st of following month',
                    'annual_income_tax' => '6 months after year end',
                    'paye' => '10th of following month'
                )
            ),
            'ZA' => array(
                'corporate_income_tax' => '28%',
                'vat_rate' => '15%',
                'withholding_tax' => '15-20% depending on transaction type',
                'small_business_incentives' => array(
                    'SBC tax rates - 0-28% graduated rates for qualifying SBCs',
                    'Turnover tax - 1-3% for micro businesses',
                    'Section 12E allowance - Manufacturing equipment incentives'
                ),
                'filing_deadlines' => array(
                    'monthly_vat' => '25th of following month',
                    'annual_income_tax' => '12 months after year end',
                    'paye' => '7th of following month'
                )
            )
        );
    }
    
    /**
     * Initialize market intelligence
     */
    private function init_market_intelligence() {
        $this->market_intelligence = array(
            'GH' => array(
                'economic_indicators' => array(
                    'gdp_growth_rate' => '3.4%',
                    'inflation_rate' => '31.7%',
                    'unemployment_rate' => '4.5%',
                    'currency_stability' => 'Moderate volatility'
                ),
                'business_opportunities' => array(
                    'Agriculture value chain development',
                    'Digital financial services',
                    'Renewable energy solutions',
                    'E-commerce and logistics',
                    'Tourism and hospitality'
                ),
                'market_challenges' => array(
                    'High inflation affecting costs',
                    'Currency depreciation',
                    'Infrastructure gaps',
                    'Access to finance for SMEs'
                )
            ),
            'KE' => array(
                'economic_indicators' => array(
                    'gdp_growth_rate' => '5.7%',
                    'inflation_rate' => '6.9%',
                    'unemployment_rate' => '5.7%',
                    'currency_stability' => 'Relatively stable'
                ),
                'business_opportunities' => array(
                    'Fintech and mobile payments',
                    'Agritech solutions',
                    'Healthcare technology',
                    'Education technology',
                    'Clean energy projects'
                ),
                'market_challenges' => array(
                    'Regulatory compliance complexity',
                    'Competition from established players',
                    'Infrastructure development needs',
                    'Skills gap in technical sectors'
                )
            ),
            'NG' => array(
                'economic_indicators' => array(
                    'gdp_growth_rate' => '3.6%',
                    'inflation_rate' => '21.3%',
                    'unemployment_rate' => '33.3%',
                    'currency_stability' => 'Significant volatility'
                ),
                'business_opportunities' => array(
                    'Fintech and digital payments',
                    'E-commerce and logistics',
                    'Entertainment and media',
                    'Agriculture and food processing',
                    'Renewable energy'
                ),
                'market_challenges' => array(
                    'Foreign exchange volatility',
                    'Security concerns in some regions',
                    'Infrastructure deficits',
                    'Regulatory uncertainty'
                )
            ),
            'ZA' => array(
                'economic_indicators' => array(
                    'gdp_growth_rate' => '1.9%',
                    'inflation_rate' => '7.1%',
                    'unemployment_rate' => '32.9%',
                    'currency_stability' => 'Moderate volatility'
                ),
                'business_opportunities' => array(
                    'Renewable energy projects',
                    'Fintech solutions',
                    'E-commerce platforms',
                    'Agritech innovations',
                    'Healthcare technology'
                ),
                'market_challenges' => array(
                    'High unemployment affecting demand',
                    'Load shedding impacting operations',
                    'Crime and security concerns',
                    'Regulatory compliance costs'
                )
            )
        );
    }
    
    /**
     * Get comprehensive country features
     */
    public function get_country_features($country_code) {
        return array(
            'general_features' => isset($this->country_features[$country_code]) ? $this->country_features[$country_code] : array(),
            'regulatory_framework' => isset($this->regulatory_frameworks[$country_code]) ? $this->regulatory_frameworks[$country_code] : array(),
            'registration_process' => isset($this->business_registration_processes[$country_code]) ? $this->business_registration_processes[$country_code] : array(),
            'tax_system' => isset($this->tax_systems[$country_code]) ? $this->tax_systems[$country_code] : array(),
            'market_intelligence' => isset($this->market_intelligence[$country_code]) ? $this->market_intelligence[$country_code] : array()
        );
    }
    
    /**
     * Get business registration guidance for specific country
     */
    public function get_registration_guidance($country_code, $business_type = 'private_limited') {
        $process = isset($this->business_registration_processes[$country_code]) ? $this->business_registration_processes[$country_code] : array();
        $regulatory = isset($this->regulatory_frameworks[$country_code]) ? $this->regulatory_frameworks[$country_code] : array();

        return array(
            'steps' => isset($process['steps']) ? $process['steps'] : array(),
            'estimated_cost' => isset($process['estimated_cost']) ? $process['estimated_cost'] : 'Contact local authorities',
            'estimated_time' => isset($process['estimated_time']) ? $process['estimated_time'] : '2-4 weeks',
            'required_documents' => isset($regulatory['business_registration']['required_documents']) ? $regulatory['business_registration']['required_documents'] : array(),
            'online_portal' => isset($regulatory['business_registration']['online_portal']) ? $regulatory['business_registration']['online_portal'] : null,
            'common_challenges' => isset($process['common_challenges']) ? $process['common_challenges'] : array()
        );
    }
    
    /**
     * Get tax compliance information
     */
    public function get_tax_compliance_info($country_code) {
        $tax_info = isset($this->tax_systems[$country_code]) ? $this->tax_systems[$country_code] : array();
        $regulatory = isset($this->regulatory_frameworks[$country_code]) ? $this->regulatory_frameworks[$country_code] : array();

        return array(
            'tax_authority' => isset($regulatory['tax_registration']['authority']) ? $regulatory['tax_registration']['authority'] : 'Local Tax Authority',
            'corporate_tax_rate' => isset($tax_info['corporate_income_tax']) ? $tax_info['corporate_income_tax'] : 'Contact tax authority',
            'vat_rate' => isset($tax_info['vat_rate']) ? $tax_info['vat_rate'] : 'Contact tax authority',
            'small_business_incentives' => isset($tax_info['small_business_incentives']) ? $tax_info['small_business_incentives'] : array(),
            'filing_deadlines' => isset($tax_info['filing_deadlines']) ? $tax_info['filing_deadlines'] : array(),
            'vat_threshold' => isset($regulatory['tax_registration']['vat_threshold']) ? $regulatory['tax_registration']['vat_threshold'] : 'Contact tax authority'
        );
    }
    
    /**
     * Get market opportunities and challenges
     */
    public function get_market_analysis($country_code) {
        $market_data = isset($this->market_intelligence[$country_code]) ? $this->market_intelligence[$country_code] : array();
        $features = isset($this->country_features[$country_code]) ? $this->country_features[$country_code] : array();

        return array(
            'economic_indicators' => isset($market_data['economic_indicators']) ? $market_data['economic_indicators'] : array(),
            'business_opportunities' => isset($market_data['business_opportunities']) ? $market_data['business_opportunities'] : array(),
            'market_challenges' => isset($market_data['market_challenges']) ? $market_data['market_challenges'] : array(),
            'key_sectors' => isset($features['business_environment']['key_sectors']) ? $features['business_environment']['key_sectors'] : array(),
            'growth_sectors' => isset($features['business_environment']['growth_sectors']) ? $features['business_environment']['growth_sectors'] : array(),
            'government_initiatives' => isset($features['business_environment']['government_initiatives']) ? $features['business_environment']['government_initiatives'] : array()
        );
    }
    
    /**
     * Generate country-specific business advice
     */
    public function generate_business_advice($country_code, $business_stage = 'startup', $industry = null) {
        $features = $this->get_country_features($country_code);
        $advice = array();
        
        // Registration advice
        if ($business_stage === 'startup' || $business_stage === 'planning') {
            $registration = $this->get_registration_guidance($country_code);
            $advice['registration'] = array(
                'title' => __('Business Registration Guidance', 'chatgabi'),
                'content' => sprintf(
                    __('To register your business in %s, you will need to complete %d main steps, with an estimated cost of %s and timeline of %s.', 'chatgabi'),
                    $this->get_country_name($country_code),
                    count($registration['steps']),
                    $registration['estimated_cost'],
                    $registration['estimated_time']
                ),
                'steps' => $registration['steps'],
                'portal' => $registration['online_portal']
            );
        }
        
        // Tax compliance advice
        $tax_info = $this->get_tax_compliance_info($country_code);
        $advice['tax_compliance'] = array(
            'title' => __('Tax Compliance Requirements', 'chatgabi'),
            'content' => sprintf(
                __('Your business will be subject to %s corporate income tax. VAT registration is required if your annual turnover exceeds %s.', 'chatgabi'),
                $tax_info['corporate_tax_rate'],
                $tax_info['vat_threshold']
            ),
            'incentives' => $tax_info['small_business_incentives'],
            'deadlines' => $tax_info['filing_deadlines']
        );
        
        // Market opportunities
        $market_analysis = $this->get_market_analysis($country_code);
        $advice['market_opportunities'] = array(
            'title' => __('Market Opportunities', 'chatgabi'),
            'content' => __('Based on current market trends and government initiatives, consider these opportunities:', 'chatgabi'),
            'opportunities' => $market_analysis['business_opportunities'],
            'growth_sectors' => $market_analysis['growth_sectors']
        );
        
        return $advice;
    }
    
    /**
     * Get country name from code
     */
    private function get_country_name($country_code) {
        $countries = array(
            'GH' => __('Ghana', 'chatgabi'),
            'KE' => __('Kenya', 'chatgabi'),
            'NG' => __('Nigeria', 'chatgabi'),
            'ZA' => __('South Africa', 'chatgabi')
        );
        return isset($countries[$country_code]) ? $countries[$country_code] : __('Ghana', 'chatgabi');
    }
}

// Initialize the country-specific features system
function chatgabi_get_country_specific_features() {
    static $features = null;
    
    if ($features === null) {
        $features = new ChatGABI_Country_Specific_Features();
    }
    
    return $features;
}
?>
