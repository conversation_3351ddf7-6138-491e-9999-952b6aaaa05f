<?php
/**
 * Country Detection System for ChatGABI
 * 
 * Advanced country detection using multiple methods including IP geolocation,
 * browser language preferences, user confirmation, and manual override
 * 
 * @package ChatGABI
 * @since 1.0.0
 * <AUTHOR> Development Team
 * @version 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Country Detection Manager
 * 
 * Manages accurate country detection for African users using multiple
 * detection methods with fallbacks and user confirmation
 * 
 * @since 1.0.0
 */
class ChatGABI_Country_Detection_System {
    
    /**
     * Singleton instance
     * 
     * @var ChatGABI_Country_Detection_System|null
     * @since 1.0.0
     */
    private static $instance = null;
    
    /**
     * Supported African countries
     * 
     * @var array
     * @since 1.0.0
     */
    private $supported_countries = array(
        'GH' => array(
            'name' => 'Ghana',
            'currency' => 'GHS',
            'language' => 'tw',
            'languages' => array('tw', 'en'),
            'timezone' => 'Africa/Accra',
            'calling_code' => '+233',
            'flag' => '🇬🇭',
            'regions' => array('Greater Accra', 'Ashanti', 'Northern', 'Western', 'Eastern', 'Central', 'Volta', 'Upper East', 'Upper West', 'Brong-Ahafo')
        ),
        'KE' => array(
            'name' => 'Kenya',
            'currency' => 'KES',
            'language' => 'sw',
            'languages' => array('sw', 'en'),
            'timezone' => 'Africa/Nairobi',
            'calling_code' => '+254',
            'flag' => '🇰🇪',
            'regions' => array('Nairobi', 'Mombasa', 'Nakuru', 'Eldoret', 'Kisumu', 'Thika', 'Malindi', 'Kitale')
        ),
        'NG' => array(
            'name' => 'Nigeria',
            'currency' => 'NGN',
            'language' => 'yo',
            'languages' => array('yo', 'en'),
            'timezone' => 'Africa/Lagos',
            'calling_code' => '+234',
            'flag' => '🇳🇬',
            'regions' => array('Lagos', 'Kano', 'Ibadan', 'Abuja', 'Port Harcourt', 'Benin City', 'Maiduguri', 'Zaria', 'Aba', 'Jos')
        ),
        'ZA' => array(
            'name' => 'South Africa',
            'currency' => 'ZAR',
            'language' => 'zu',
            'languages' => array('zu', 'en'),
            'timezone' => 'Africa/Johannesburg',
            'calling_code' => '+27',
            'flag' => '🇿🇦',
            'regions' => array('Johannesburg', 'Cape Town', 'Durban', 'Pretoria', 'Port Elizabeth', 'Bloemfontein', 'East London', 'Nelspruit', 'Polokwane')
        )
    );
    
    /**
     * Detection methods and their weights
     * 
     * @var array
     * @since 1.0.0
     */
    private $detection_methods = array(
        'ip_geolocation' => array(
            'weight' => 40,
            'sources' => array('ipapi', 'ipinfo', 'geoip2'),
            'cache_duration' => 3600 // 1 hour
        ),
        'browser_language' => array(
            'weight' => 20,
            'cache_duration' => 86400 // 24 hours
        ),
        'user_timezone' => array(
            'weight' => 15,
            'cache_duration' => 86400 // 24 hours
        ),
        'user_preference' => array(
            'weight' => 100, // Highest priority
            'cache_duration' => 2592000 // 30 days
        ),
        'session_history' => array(
            'weight' => 25,
            'cache_duration' => 86400 // 24 hours
        )
    );
    
    /**
     * IP geolocation API sources
     * 
     * @var array
     * @since 1.0.0
     */
    private $ip_apis = array(
        'primary' => array(
            'name' => 'IP-API',
            'url' => 'http://ip-api.com/json/',
            'key_required' => false,
            'rate_limit' => 1000, // per minute
            'timeout' => 5
        ),
        'secondary' => array(
            'name' => 'IPInfo',
            'url' => 'https://ipinfo.io/',
            'key_required' => true,
            'rate_limit' => 50000, // per month
            'timeout' => 5
        ),
        'tertiary' => array(
            'name' => 'MaxMind',
            'url' => 'https://geoip.maxmind.com/geoip/v2.1/country/',
            'key_required' => true,
            'rate_limit' => 1000, // per day
            'timeout' => 5
        )
    );
    
    /**
     * Detection statistics
     * 
     * @var array
     * @since 1.0.0
     */
    private $detection_stats = array();
    
    /**
     * Current user's detected country info
     * 
     * @var array
     * @since 1.0.0
     */
    private $current_detection = array();
    
    /**
     * Get singleton instance
     * 
     * @return ChatGABI_Country_Detection_System Instance of the country detection system
     * @since 1.0.0
     */
    public static function get_instance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Private constructor to enforce singleton pattern
     * 
     * @since 1.0.0
     */
    private function __construct() {
        $this->init_hooks();
        $this->init_detection_stats();
        $this->detect_user_country();
    }
    
    /**
     * Initialize WordPress hooks
     * 
     * @since 1.0.0
     */
    private function init_hooks() {
        // Admin hooks
        if (is_admin()) {
            add_action('admin_menu', array($this, 'add_country_admin_menu'));
            add_action('admin_notices', array($this, 'show_country_notices'));
        }
        
        // AJAX hooks for country detection
        add_action('wp_ajax_chatgabi_detect_country', array($this, 'ajax_detect_country'));
        add_action('wp_ajax_nopriv_chatgabi_detect_country', array($this, 'ajax_detect_country'));
        add_action('wp_ajax_chatgabi_set_country_preference', array($this, 'ajax_set_country_preference'));
        add_action('wp_ajax_nopriv_chatgabi_set_country_preference', array($this, 'ajax_set_country_preference'));
        add_action('wp_ajax_chatgabi_get_country_suggestions', array($this, 'ajax_get_country_suggestions'));
        add_action('wp_ajax_nopriv_chatgabi_get_country_suggestions', array($this, 'ajax_get_country_suggestions'));
        
        // Frontend hooks
        add_action('wp_enqueue_scripts', array($this, 'enqueue_country_detection_scripts'));
        add_action('wp_footer', array($this, 'render_country_confirmation_modal'));
        
        // REST API endpoints
        add_action('rest_api_init', array($this, 'register_rest_endpoints'));
        
        // User hooks
        add_action('wp_login', array($this, 'update_user_country_on_login'), 10, 2);
        add_action('user_register', array($this, 'detect_country_on_registration'));
        
        // Country-based content hooks
        add_filter('chatgabi_get_user_country', array($this, 'get_user_country'));
        add_filter('chatgabi_get_user_currency', array($this, 'get_user_currency'));
        add_filter('chatgabi_get_user_language', array($this, 'get_user_language'));
    }
    
    /**
     * Initialize detection statistics
     * 
     * @since 1.0.0
     */
    private function init_detection_stats() {
        $this->detection_stats = get_option('chatgabi_country_detection_stats', array(
            'total_detections' => 0,
            'ip_detections' => 0,
            'language_detections' => 0,
            'timezone_detections' => 0,
            'user_confirmations' => 0,
            'manual_overrides' => 0,
            'accuracy_rate' => 0,
            'last_updated' => time()
        ));
    }
    
    /**
     * Detect user's country using multiple methods
     * 
     * @param bool $force_refresh Force new detection instead of using cache
     * @return array Detection result with country info and confidence level
     * @since 1.0.0
     */
    public function detect_user_country($force_refresh = false) {
        $user_id = get_current_user_id();
        $session_id = $this->get_session_id();
        
        // Check for existing user preference first
        if ($user_id > 0) {
            $user_country = get_user_meta($user_id, 'chatgabi_country_preference', true);
            if (!empty($user_country) && !$force_refresh) {
                $this->current_detection = array(
                    'country_code' => $user_country,
                    'country_info' => $this->supported_countries[$user_country] ?? array(),
                    'detection_method' => 'user_preference',
                    'confidence' => 100,
                    'timestamp' => time(),
                    'requires_confirmation' => false
                );
                return $this->current_detection;
            }
        }
        
        // Check session cache
        if (!$force_refresh) {
            $cached_detection = $this->get_cached_detection($session_id);
            if ($cached_detection !== false) {
                $this->current_detection = $cached_detection;
                return $this->current_detection;
            }
        }
        
        // Perform multi-method detection
        $detection_results = array();
        
        // Method 1: IP Geolocation
        $ip_result = $this->detect_by_ip_geolocation();
        if ($ip_result !== false) {
            $detection_results['ip_geolocation'] = $ip_result;
        }
        
        // Method 2: Browser Language
        $language_result = $this->detect_by_browser_language();
        if ($language_result !== false) {
            $detection_results['browser_language'] = $language_result;
        }
        
        // Method 3: User Timezone
        $timezone_result = $this->detect_by_timezone();
        if ($timezone_result !== false) {
            $detection_results['user_timezone'] = $timezone_result;
        }
        
        // Method 4: Session History
        $history_result = $this->detect_by_session_history($session_id);
        if ($history_result !== false) {
            $detection_results['session_history'] = $history_result;
        }
        
        // Combine results using weighted algorithm
        $final_detection = $this->combine_detection_results($detection_results);
        
        // Cache the result
        $this->cache_detection($session_id, $final_detection);
        
        // Update statistics
        $this->update_detection_stats($final_detection);
        
        $this->current_detection = $final_detection;
        return $final_detection;
    }
    
    /**
     * Detect country by IP geolocation
     * 
     * @return array|false Detection result or false on failure
     * @since 1.0.0
     */
    private function detect_by_ip_geolocation() {
        $user_ip = $this->get_user_ip();
        
        // Skip detection for local/private IPs
        if ($this->is_private_ip($user_ip)) {
            return false;
        }
        
        // Try each IP API source
        foreach ($this->ip_apis as $source_name => $api_config) {
            try {
                $result = $this->call_ip_api($api_config, $user_ip);
                
                if ($result !== false && isset($result['country_code'])) {
                    $country_code = strtoupper($result['country_code']);
                    
                    // Only return if it's a supported African country
                    if (isset($this->supported_countries[$country_code])) {
                        $this->detection_stats['ip_detections']++;
                        
                        return array(
                            'country_code' => $country_code,
                            'method' => 'ip_geolocation',
                            'source' => $api_config['name'],
                            'confidence' => $result['confidence'] ?? 75,
                            'additional_data' => $result
                        );
                    }
                }
            } catch (Exception $e) {
                error_log("ChatGABI IP Detection Error ({$api_config['name']}): " . $e->getMessage());
                continue;
            }
        }
        
        return false;
    }
    
    /**
     * Call IP geolocation API
     * 
     * @param array $api_config API configuration
     * @param string $ip_address IP address to lookup
     * @return array|false API result or false on failure
     * @since 1.0.0
     */
    private function call_ip_api($api_config, $ip_address) {
        $url = $api_config['url'];
        $timeout = $api_config['timeout'];
        
        // Build API URL based on service
        switch ($api_config['name']) {
            case 'IP-API':
                $url .= $ip_address . '?fields=status,country,countryCode,region,city,timezone,isp';
                break;
                
            case 'IPInfo':
                $api_key = defined('CHATGABI_IPINFO_API_KEY') ? CHATGABI_IPINFO_API_KEY : '';
                $url .= $ip_address;
                if (!empty($api_key)) {
                    $url .= '?token=' . $api_key;
                }
                break;
                
            case 'MaxMind':
                $api_key = defined('CHATGABI_MAXMIND_API_KEY') ? CHATGABI_MAXMIND_API_KEY : '';
                if (empty($api_key)) {
                    return false;
                }
                $url .= $ip_address;
                break;
        }
        
        // Make API request
        $response = wp_remote_get($url, array(
            'timeout' => $timeout,
            'headers' => array(
                'User-Agent' => 'ChatGABI/1.0 (+https://chatgabi.com)',
                'Accept' => 'application/json'
            )
        ));
        
        if (is_wp_error($response)) {
            throw new Exception('API request failed: ' . $response->get_error_message());
        }
        
        $body = wp_remote_retrieve_body($response);
        $data = json_decode($body, true);
        
        if (!$data) {
            throw new Exception('Invalid JSON response');
        }
        
        // Parse response based on service
        return $this->parse_ip_api_response($api_config['name'], $data);
    }
    
    /**
     * Parse IP API response
     * 
     * @param string $service_name API service name
     * @param array $data API response data
     * @return array|false Parsed result or false on failure
     * @since 1.0.0
     */
    private function parse_ip_api_response($service_name, $data) {
        switch ($service_name) {
            case 'IP-API':
                if (isset($data['countryCode']) && $data['status'] === 'success') {
                    return array(
                        'country_code' => $data['countryCode'],
                        'country_name' => $data['country'] ?? '',
                        'region' => $data['region'] ?? '',
                        'city' => $data['city'] ?? '',
                        'timezone' => $data['timezone'] ?? '',
                        'isp' => $data['isp'] ?? '',
                        'confidence' => 80
                    );
                }
                break;
                
            case 'IPInfo':
                if (isset($data['country'])) {
                    return array(
                        'country_code' => $data['country'],
                        'country_name' => '',
                        'region' => $data['region'] ?? '',
                        'city' => $data['city'] ?? '',
                        'timezone' => $data['timezone'] ?? '',
                        'org' => $data['org'] ?? '',
                        'confidence' => 85
                    );
                }
                break;
                
            case 'MaxMind':
                if (isset($data['country']['iso_code'])) {
                    return array(
                        'country_code' => $data['country']['iso_code'],
                        'country_name' => $data['country']['names']['en'] ?? '',
                        'confidence' => $data['country']['confidence'] ?? 90
                    );
                }
                break;
        }
        
        return false;
    }
    
    /**
     * Detect country by browser language preferences
     * 
     * @return array|false Detection result or false on failure
     * @since 1.0.0
     */
    private function detect_by_browser_language() {
        $accept_language = $_SERVER['HTTP_ACCEPT_LANGUAGE'] ?? '';
        
        if (empty($accept_language)) {
            return false;
        }
        
        // Parse Accept-Language header
        $languages = array();
        foreach (explode(',', $accept_language) as $lang) {
            $parts = explode(';', trim($lang));
            $locale = trim($parts[0]);
            $quality = 1.0;
            
            if (isset($parts[1]) && strpos($parts[1], 'q=') === 0) {
                $quality = floatval(substr($parts[1], 2));
            }
            
            $languages[$locale] = $quality;
        }
        
        // Sort by quality
        arsort($languages);
        
        // Map languages to countries
        $language_country_map = array(
            'tw' => 'GH',
            'sw' => 'KE',
            'yo' => 'NG',
            'zu' => 'ZA',
            'en-GH' => 'GH',
            'en-KE' => 'KE',
            'en-NG' => 'NG',
            'en-ZA' => 'ZA'
        );
        
        foreach ($languages as $locale => $quality) {
            // Check direct language match
            if (isset($language_country_map[$locale])) {
                $this->detection_stats['language_detections']++;
                
                return array(
                    'country_code' => $language_country_map[$locale],
                    'method' => 'browser_language',
                    'confidence' => min(90, $quality * 100),
                    'detected_language' => $locale
                );
            }
            
            // Check country code in locale (e.g., en-GH)
            if (strpos($locale, '-') !== false) {
                list($lang, $country) = explode('-', $locale, 2);
                $country = strtoupper($country);
                
                if (isset($this->supported_countries[$country])) {
                    $this->detection_stats['language_detections']++;
                    
                    return array(
                        'country_code' => $country,
                        'method' => 'browser_language',
                        'confidence' => min(85, $quality * 95),
                        'detected_language' => $locale
                    );
                }
            }
        }
        
        return false;
    }
    
    /**
     * Detect country by user timezone
     * 
     * @return array|false Detection result or false on failure
     * @since 1.0.0
     */
    private function detect_by_timezone() {
        // This would typically get timezone from JavaScript
        // For now, we'll check if it's been set via AJAX
        $user_timezone = '';
        
        if (isset($_COOKIE['chatgabi_timezone'])) {
            $user_timezone = sanitize_text_field($_COOKIE['chatgabi_timezone']);
        }
        
        if (empty($user_timezone)) {
            return false;
        }
        
        // Map timezones to countries
        $timezone_country_map = array(
            'Africa/Accra' => 'GH',
            'Africa/Nairobi' => 'KE',
            'Africa/Lagos' => 'NG',
            'Africa/Johannesburg' => 'ZA',
            'GMT' => 'GH', // Ghana uses GMT
            'EAT' => 'KE', // East Africa Time (Kenya)
            'WAT' => 'NG', // West Africa Time (Nigeria)
            'SAST' => 'ZA'  // South Africa Standard Time
        );
        
        foreach ($timezone_country_map as $timezone => $country) {
            if (strpos($user_timezone, $timezone) !== false) {
                $this->detection_stats['timezone_detections']++;
                
                return array(
                    'country_code' => $country,
                    'method' => 'user_timezone',
                    'confidence' => 70,
                    'detected_timezone' => $user_timezone
                );
            }
        }
        
        return false;
    }
    
    /**
     * Detect country by session history
     * 
     * @param string $session_id Session identifier
     * @return array|false Detection result or false on failure
     * @since 1.0.0
     */
    private function detect_by_session_history($session_id) {
        $history_key = "chatgabi_country_history_{$session_id}";
        $country_history = get_transient($history_key);
        
        if (!$country_history || !is_array($country_history)) {
            return false;
        }
        
        // Count occurrences of each country
        $country_counts = array_count_values($country_history);
        
        // Get most frequent country
        arsort($country_counts);
        $most_frequent_country = key($country_counts);
        
        if ($most_frequent_country && isset($this->supported_countries[$most_frequent_country])) {
            $frequency = $country_counts[$most_frequent_country];
            $total_visits = count($country_history);
            $confidence = min(90, ($frequency / $total_visits) * 100);
            
            return array(
                'country_code' => $most_frequent_country,
                'method' => 'session_history',
                'confidence' => $confidence,
                'visit_frequency' => $frequency,
                'total_visits' => $total_visits
            );
        }
        
        return false;
    }
    
    /**
     * Combine multiple detection results using weighted algorithm
     * 
     * @param array $detection_results Array of detection results from different methods
     * @return array Final detection result
     * @since 1.0.0
     */
    private function combine_detection_results($detection_results) {
        if (empty($detection_results)) {
            return array(
                'country_code' => 'NG', // Default to Nigeria
                'country_info' => $this->supported_countries['NG'],
                'detection_method' => 'default',
                'confidence' => 10,
                'timestamp' => time(),
                'requires_confirmation' => true,
                'all_results' => array()
            );
        }
        
        $country_scores = array();
        
        // Calculate weighted scores for each country
        foreach ($detection_results as $method => $result) {
            $country_code = $result['country_code'];
            $method_weight = $this->detection_methods[$method]['weight'] ?? 10;
            $confidence = $result['confidence'] ?? 50;
            
            $score = ($method_weight * $confidence) / 100;
            
            if (!isset($country_scores[$country_code])) {
                $country_scores[$country_code] = 0;
            }
            
            $country_scores[$country_code] += $score;
        }
        
        // Get country with highest score
        arsort($country_scores);
        $detected_country = key($country_scores);
        $final_confidence = min(100, $country_scores[$detected_country]);
        
        // Determine primary detection method
        $primary_method = 'combined';
        foreach ($detection_results as $method => $result) {
            if ($result['country_code'] === $detected_country) {
                $primary_method = $method;
                break;
            }
        }
        
        return array(
            'country_code' => $detected_country,
            'country_info' => $this->supported_countries[$detected_country] ?? array(),
            'detection_method' => $primary_method,
            'confidence' => round($final_confidence, 1),
            'timestamp' => time(),
            'requires_confirmation' => $final_confidence < 80,
            'all_results' => $detection_results,
            'country_scores' => $country_scores
        );
    }
    
    /**
     * Get user's IP address
     * 
     * @return string User's IP address
     * @since 1.0.0
     */
    private function get_user_ip() {
        $ip_keys = array(
            'HTTP_CF_CONNECTING_IP',     // Cloudflare
            'HTTP_CLIENT_IP',            // Proxy
            'HTTP_X_FORWARDED_FOR',      // Load balancer/proxy
            'HTTP_X_FORWARDED',          // Proxy
            'HTTP_X_CLUSTER_CLIENT_IP',  // Cluster
            'HTTP_FORWARDED_FOR',        // Proxy
            'HTTP_FORWARDED',            // Proxy
            'REMOTE_ADDR'                // Standard
        );
        
        foreach ($ip_keys as $key) {
            if (!empty($_SERVER[$key])) {
                $ip = $_SERVER[$key];
                
                // Handle comma-separated IPs
                if (strpos($ip, ',') !== false) {
                    $ip = trim(explode(',', $ip)[0]);
                }
                
                // Validate IP
                if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                    return $ip;
                }
            }
        }
        
        return $_SERVER['REMOTE_ADDR'] ?? '127.0.0.1';
    }
    
    /**
     * Check if IP is private/local
     * 
     * @param string $ip IP address
     * @return bool Whether IP is private
     * @since 1.0.0
     */
    private function is_private_ip($ip) {
        return !filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE);
    }
    
    /**
     * Get session identifier
     * 
     * @return string Session identifier
     * @since 1.0.0
     */
    private function get_session_id() {
        if (!session_id()) {
            session_start();
        }
        
        if (!isset($_SESSION['chatgabi_session_id'])) {
            $_SESSION['chatgabi_session_id'] = wp_generate_uuid4();
        }
        
        return $_SESSION['chatgabi_session_id'];
    }
    
    /**
     * Cache detection result
     * 
     * @param string $session_id Session identifier
     * @param array $detection Detection result
     * @since 1.0.0
     */
    private function cache_detection($session_id, $detection) {
        $cache_key = "chatgabi_detection_{$session_id}";
        $cache_duration = 3600; // 1 hour
        
        // Cache in Redis if available
        if (class_exists('ChatGABI_Redis_Caching_System')) {
            $redis_cache = ChatGABI_Redis_Caching_System::get_instance();
            $redis_cache->set($cache_key, $detection, 'country_detection', $cache_duration);
        }
        
        // Cache in WordPress transients
        set_transient($cache_key, $detection, $cache_duration);
        
        // Update session history
        $this->update_session_history($session_id, $detection['country_code']);
    }
    
    /**
     * Get cached detection result
     * 
     * @param string $session_id Session identifier
     * @return array|false Cached detection or false if not found
     * @since 1.0.0
     */
    private function get_cached_detection($session_id) {
        $cache_key = "chatgabi_detection_{$session_id}";
        
        // Try Redis cache first if available
        if (class_exists('ChatGABI_Redis_Caching_System')) {
            $redis_cache = ChatGABI_Redis_Caching_System::get_instance();
            $cached_data = $redis_cache->get($cache_key, 'country_detection');
            
            if ($cached_data !== false) {
                return $cached_data;
            }
        }
        
        // Try WordPress transients
        return get_transient($cache_key);
    }
    
    /**
     * Update session history
     *
     * @param string $session_id Session identifier
     * @param string $country_code Country code
     * @since 1.0.0
     */
    private function update_session_history($session_id, $country_code) {
        $history_key = "chatgabi_country_history_{$session_id}";
        $history = get_transient($history_key) ?: array();

        // Add country to history (keep last 10)
        array_unshift($history, array(
            'country_code' => $country_code,
            'timestamp' => time()
        ));

        // Keep only last 10 entries
        $history = array_slice($history, 0, 10);

        // Save updated history
        set_transient($history_key, $history, 86400); // 24 hours
    }
}

// Initialize the country detection system
function chatgabi_get_country_detection_system() {
    return ChatGABI_Country_Detection_System::get_instance();
}

// Helper functions for other modules
if (!function_exists('chatgabi_get_user_country')) {
    function chatgabi_get_user_country() {
        $detection_system = chatgabi_get_country_detection_system();
        $detection = $detection_system->detect_user_country();
        return $detection['country_code'] ?? 'GH'; // Default to Ghana
    }
}

if (!function_exists('chatgabi_get_user_currency')) {
    function chatgabi_get_user_currency() {
        $detection_system = chatgabi_get_country_detection_system();
        $detection = $detection_system->detect_user_country();
        $country_code = $detection['country_code'] ?? 'GH';

        $currencies = array(
            'GH' => 'GHS',
            'KE' => 'KES',
            'NG' => 'NGN',
            'ZA' => 'ZAR'
        );

        return $currencies[$country_code] ?? 'GHS';
    }
}

if (!function_exists('chatgabi_get_user_language')) {
    function chatgabi_get_user_language() {
        $detection_system = chatgabi_get_country_detection_system();
        $detection = $detection_system->detect_user_country();
        $country_code = $detection['country_code'] ?? 'GH';

        $languages = array(
            'GH' => 'tw',
            'KE' => 'sw',
            'NG' => 'yo',
            'ZA' => 'zu'
        );

        return $languages[$country_code] ?? 'en';
    }
}
