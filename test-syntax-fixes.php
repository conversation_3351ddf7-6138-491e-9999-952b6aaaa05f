<?php
/**
 * ChatGABI Syntax Fixes Validation Script
 * 
 * Tests the syntax fixes for country-specific-features.php and country-detection-system.php
 */

// Load WordPress
require_once dirname(__FILE__) . '/wp-config.php';
require_once ABSPATH . 'wp-load.php';

?>
<!DOCTYPE html>
<html>
<head>
    <title>ChatGABI Syntax Fixes Validation</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f1f1f1; }
        .container { max-width: 1000px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; }
        .test-section { margin: 20px 0; padding: 15px; border-left: 4px solid #007cba; background: #f9f9f9; }
        .success { border-left-color: #00a32a; background: #f0f9f0; }
        .error { border-left-color: #d63638; background: #fef0f0; }
        .test-result { padding: 10px; margin: 5px 0; border-radius: 4px; }
        .pass { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .fail { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .code { background: #f8f9fa; padding: 10px; border-radius: 4px; font-family: monospace; margin: 10px 0; }
        .file-info { background: #e9ecef; padding: 8px; border-radius: 4px; margin: 5px 0; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 ChatGABI Syntax Fixes Validation</h1>
        <p>Testing the syntax fixes for critical PHP files in the ChatGABI theme...</p>

        <?php
        $start_time = microtime(true);
        $tests_passed = 0;
        $total_tests = 0;
        $syntax_errors = 0;
        
        // Files to test
        $test_files = array(
            'country-specific-features.php' => get_template_directory() . '/inc/country-specific-features.php',
            'country-detection-system.php' => get_template_directory() . '/inc/country-detection-system.php'
        );
        
        // Test 1: PHP Syntax Validation
        echo "<div class='test-section'>";
        echo "<h2>🔍 Test 1: PHP Syntax Validation</h2>";
        
        foreach ($test_files as $file_name => $file_path) {
            $total_tests++;
            
            echo "<div class='file-info'>";
            echo "<strong>Testing:</strong> {$file_name}";
            echo "</div>";
            
            if (!file_exists($file_path)) {
                echo "<div class='test-result fail'>❌ File not found: {$file_path}</div>";
                $syntax_errors++;
                continue;
            }
            
            // Test PHP syntax using php -l
            $output = array();
            $return_code = 0;
            
            // Use exec to check syntax
            $command = "php -l " . escapeshellarg($file_path) . " 2>&1";
            exec($command, $output, $return_code);
            
            if ($return_code === 0) {
                echo "<div class='test-result pass'>✅ Syntax OK</div>";
                $tests_passed++;
            } else {
                echo "<div class='test-result fail'>❌ Syntax Error:</div>";
                echo "<div class='code'>" . implode("\n", $output) . "</div>";
                $syntax_errors++;
            }
        }
        
        echo "</div>";
        
        // Test 2: File Loading Test
        echo "<div class='test-section'>";
        echo "<h2>📂 Test 2: File Loading Test</h2>";
        
        foreach ($test_files as $file_name => $file_path) {
            $total_tests++;
            
            echo "<div class='file-info'>";
            echo "<strong>Loading:</strong> {$file_name}";
            echo "</div>";
            
            try {
                // Capture any output/errors during include
                ob_start();
                $error_before = error_get_last();
                
                include_once $file_path;
                
                $error_after = error_get_last();
                $output = ob_get_clean();
                
                // Check if new errors occurred
                if ($error_after && $error_after !== $error_before) {
                    throw new Exception("PHP Error: " . $error_after['message']);
                }
                
                echo "<div class='test-result pass'>✅ File loaded successfully</div>";
                $tests_passed++;
                
                if (!empty($output)) {
                    echo "<div class='code'>Output: " . htmlspecialchars($output) . "</div>";
                }
                
            } catch (Exception $e) {
                echo "<div class='test-result fail'>❌ Loading failed: " . $e->getMessage() . "</div>";
            } catch (ParseError $e) {
                echo "<div class='test-result fail'>❌ Parse Error: " . $e->getMessage() . "</div>";
            } catch (Error $e) {
                echo "<div class='test-result fail'>❌ Fatal Error: " . $e->getMessage() . "</div>";
            }
        }
        
        echo "</div>";
        
        // Test 3: Class Instantiation Test
        echo "<div class='test-section'>";
        echo "<h2>🏗️ Test 3: Class Instantiation Test</h2>";
        
        $classes_to_test = array(
            'ChatGABI_Country_Specific_Features' => 'country-specific-features.php',
            'ChatGABI_Country_Detection_System' => 'country-detection-system.php'
        );
        
        foreach ($classes_to_test as $class_name => $source_file) {
            $total_tests++;
            
            echo "<div class='file-info'>";
            echo "<strong>Testing Class:</strong> {$class_name} (from {$source_file})";
            echo "</div>";
            
            try {
                if (class_exists($class_name)) {
                    // Try to instantiate the class
                    if ($class_name === 'ChatGABI_Country_Detection_System') {
                        // Use singleton pattern
                        $instance = ChatGABI_Country_Detection_System::get_instance();
                    } else {
                        // Regular instantiation
                        $instance = new $class_name();
                    }
                    
                    if ($instance) {
                        echo "<div class='test-result pass'>✅ Class instantiated successfully</div>";
                        $tests_passed++;
                    } else {
                        echo "<div class='test-result fail'>❌ Class instantiation returned null</div>";
                    }
                } else {
                    echo "<div class='test-result fail'>❌ Class not found: {$class_name}</div>";
                }
            } catch (Exception $e) {
                echo "<div class='test-result fail'>❌ Instantiation failed: " . $e->getMessage() . "</div>";
            } catch (Error $e) {
                echo "<div class='test-result fail'>❌ Fatal Error during instantiation: " . $e->getMessage() . "</div>";
            }
        }
        
        echo "</div>";
        
        // Test 4: Function Availability Test
        echo "<div class='test-section'>";
        echo "<h2>🔧 Test 4: Function Availability Test</h2>";
        
        $functions_to_test = array(
            'chatgabi_get_country_specific_features',
            'chatgabi_get_country_detection_system',
            'chatgabi_get_user_country',
            'chatgabi_get_user_currency',
            'chatgabi_get_user_language'
        );
        
        foreach ($functions_to_test as $function_name) {
            $total_tests++;
            
            if (function_exists($function_name)) {
                echo "<div class='test-result pass'>✅ Function available: {$function_name}()</div>";
                $tests_passed++;
            } else {
                echo "<div class='test-result fail'>❌ Function not found: {$function_name}()</div>";
            }
        }
        
        echo "</div>";
        
        // Test 5: Functionality Test
        echo "<div class='test-section'>";
        echo "<h2>⚙️ Test 5: Basic Functionality Test</h2>";
        
        // Test country-specific features
        $total_tests++;
        try {
            if (function_exists('chatgabi_get_country_specific_features')) {
                $features = chatgabi_get_country_specific_features();
                $ghana_features = $features->get_country_features('GH');
                
                if (!empty($ghana_features)) {
                    echo "<div class='test-result pass'>✅ Country-specific features working</div>";
                    $tests_passed++;
                } else {
                    echo "<div class='test-result fail'>❌ Country-specific features returned empty</div>";
                }
            } else {
                echo "<div class='test-result fail'>❌ Country-specific features function not available</div>";
            }
        } catch (Exception $e) {
            echo "<div class='test-result fail'>❌ Country-specific features error: " . $e->getMessage() . "</div>";
        }
        
        // Test country detection
        $total_tests++;
        try {
            if (function_exists('chatgabi_get_user_country')) {
                $user_country = chatgabi_get_user_country();
                
                if (!empty($user_country)) {
                    echo "<div class='test-result pass'>✅ Country detection working (detected: {$user_country})</div>";
                    $tests_passed++;
                } else {
                    echo "<div class='test-result fail'>❌ Country detection returned empty</div>";
                }
            } else {
                echo "<div class='test-result fail'>❌ Country detection function not available</div>";
            }
        } catch (Exception $e) {
            echo "<div class='test-result fail'>❌ Country detection error: " . $e->getMessage() . "</div>";
        }
        
        echo "</div>";
        
        // Final Results
        $end_time = microtime(true);
        $total_execution_time = $end_time - $start_time;
        $success_rate = round(($tests_passed / $total_tests) * 100, 1);
        
        $result_class = ($syntax_errors == 0 && $success_rate >= 90) ? 'success' : 'error';
        
        echo "<div class='test-section {$result_class}'>";
        echo "<h2>📋 Final Results</h2>";
        
        echo "<div class='code'>";
        echo "Tests Passed: {$tests_passed}/{$total_tests} ({$success_rate}%)<br>";
        echo "Syntax Errors: {$syntax_errors}<br>";
        echo "Execution Time: " . round($total_execution_time, 3) . "s<br>";
        echo "Peak Memory: " . round(memory_get_peak_usage(true) / 1024 / 1024, 2) . "MB";
        echo "</div>";
        
        if ($syntax_errors == 0 && $success_rate >= 90) {
            echo "<div class='test-result pass'>";
            echo "🎉 <strong>SUCCESS!</strong> All syntax errors have been fixed and ChatGABI African market features are working correctly.";
            echo "</div>";
            
            echo "<h3>✅ What's Fixed:</h3>";
            echo "<ul>";
            echo "<li><strong>country-specific-features.php:</strong> Fixed array key syntax with colons</li>";
            echo "<li><strong>country-detection-system.php:</strong> Completed incomplete function and closed class properly</li>";
            echo "<li><strong>Functionality:</strong> All ChatGABI African market customization features preserved</li>";
            echo "<li><strong>Compatibility:</strong> Timeout prevention system integration maintained</li>";
            echo "</ul>";
            
            echo "<h3>🚀 Next Steps:</h3>";
            echo "<ul>";
            echo "<li>Test ChatGABI homepage: <a href='" . home_url() . "'>" . home_url() . "</a></li>";
            echo "<li>Run timeout fix test: <a href='test-timeout-fixes.php'>test-timeout-fixes.php</a></li>";
            echo "<li>Use diagnostic tool: <a href='chatgabi-timeout-fix.php'>chatgabi-timeout-fix.php</a></li>";
            echo "<li>Test African market features in the ChatGABI interface</li>";
            echo "</ul>";
        } else {
            echo "<div class='test-result fail'>";
            echo "❌ <strong>ISSUES DETECTED:</strong> Some problems remain. Please review the failed tests above.";
            echo "</div>";
            
            if ($syntax_errors > 0) {
                echo "<p><strong>Syntax Errors:</strong> {$syntax_errors} files still have syntax issues.</p>";
            }
            
            echo "<h3>🔧 Troubleshooting:</h3>";
            echo "<ul>";
            echo "<li>Review any remaining syntax errors in the test output above</li>";
            echo "<li>Check PHP error logs for additional details</li>";
            echo "<li>Ensure all required WordPress functions are available</li>";
            echo "<li>Verify file permissions and accessibility</li>";
            echo "</ul>";
        }
        
        echo "</div>";
        ?>
    </div>
</body>
</html>
